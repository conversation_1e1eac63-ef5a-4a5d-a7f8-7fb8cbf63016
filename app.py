# app.py

import os
from flask import Flask

app = Flask(__name__)

# --- 从环境变量读取配置信息 ---
# 这些信息可以通过Docker环境变量动态配置
PROXY_IP = os.getenv('PROXY_IP', '*************')  # 默认值为原来的IP
PROXY_PORT = int(os.getenv('PROXY_PORT', '2018'))   # 默认值为原来的端口
API_TOKEN = os.getenv('API_TOKEN', 'zdce10v1')      # API标识符
API_KEY = os.getenv('API_KEY', 'mewJiWUG')          # API密钥

@app.route('/proxy_info', methods=['GET'])
def get_proxy_info():
    """
    提供代理IP和端口的API接口
    通过环境变量动态配置返回信息
    """
    return f'{PROXY_IP}:{PROXY_PORT} {API_TOKEN} {API_KEY}'

if __name__ == '__main__':
    # Flask默认监听在5000端口，只允许本地访问
    # 为了让外部访问这个API，我们需要监听0.0.0.0
    # 注意：直接在生产环境中使用 app.run() 不安全，推荐使用Gunicorn/Nginx
    app.run(host='0.0.0.0', port=5001, debug=False)
